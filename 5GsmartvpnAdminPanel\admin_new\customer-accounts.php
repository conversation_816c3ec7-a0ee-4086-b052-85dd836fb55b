<?php
/**
 * Admin Panel - Customer Accounts Management
 * Manage customer accounts and their activities
 */

session_start();
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
        header('Location: login.php');
        exit();
    }
}

$page_title = "Customer Accounts";
$success = '';
$error = '';

// Add Bootstrap CSS and JS for modals
$additional_css = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'
];
$additional_js = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
];

// Handle customer account actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_status'])) {
        $customer_id = (int)$_POST['customer_id'];
        $status = $_POST['status'];
        
        $stmt = $conn->prepare("UPDATE customer_accounts SET status = ? WHERE id = ?");
        $stmt->bind_param("si", $status, $customer_id);
        
        if ($stmt->execute()) {
            $success = "Customer status updated successfully!";
        } else {
            $error = "Failed to update customer status.";
        }
    }
    
    if (isset($_POST['add_notes'])) {
        $customer_id = (int)$_POST['customer_id'];
        $notes = trim($_POST['notes']);
        
        $stmt = $conn->prepare("UPDATE customer_accounts SET admin_notes = ? WHERE id = ?");
        $stmt->bind_param("si", $notes, $customer_id);
        
        if ($stmt->execute()) {
            $success = "Customer notes updated successfully!";
        } else {
            $error = "Failed to update customer notes.";
        }
    }
}

// Get customer statistics
$stats = [];
$stats_query = "
    SELECT 
        status,
        COUNT(*) as count
    FROM customer_accounts 
    GROUP BY status
";
$stats_result = mysqli_query($conn, $stats_query);
if ($stats_result) {
    while ($row = mysqli_fetch_assoc($stats_result)) {
        $stats[$row['status']] = $row['count'];
    }
}

// Get total revenue per customer
$revenue_query = "
    SELECT 
        customer_id,
        SUM(amount) as total_spent
    FROM customer_payments 
    WHERE payment_status = 'verified'
    GROUP BY customer_id
";
$revenue_result = mysqli_query($conn, $revenue_query);
$customer_revenue = [];
if ($revenue_result) {
    while ($row = mysqli_fetch_assoc($revenue_result)) {
        $customer_revenue[$row['customer_id']] = $row['total_spent'];
    }
}

// Get customers with their activity data
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';

$where_conditions = [];
if ($filter !== 'all') {
    $where_conditions[] = "ca.status = '" . mysqli_real_escape_string($conn, $filter) . "'";
}
if (!empty($search)) {
    $search_term = mysqli_real_escape_string($conn, $search);
    $where_conditions[] = "(ca.customer_name LIKE '%$search_term%' OR ca.whatsapp_number LIKE '%$search_term%')";
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Check if customer_id column exists in custom_ads table
$columns_check = mysqli_query($conn, "SHOW COLUMNS FROM custom_ads LIKE 'customer_id'");
$has_customer_id = mysqli_num_rows($columns_check) > 0;

if ($has_customer_id) {
    $customers_query = "
        SELECT
            ca.*,
            COUNT(DISTINCT cp.id) as total_payments,
            COUNT(DISTINCT cads.id) as total_ads,
            MAX(cp.created_at) as last_payment_date
        FROM customer_accounts ca
        LEFT JOIN customer_payments cp ON ca.id = cp.customer_id
        LEFT JOIN custom_ads cads ON ca.id = cads.customer_id
        $where_clause
        GROUP BY ca.id
        ORDER BY ca.created_at DESC
    ";
} else {
    $customers_query = "
        SELECT
            ca.*,
            COUNT(DISTINCT cp.id) as total_payments,
            0 as total_ads,
            MAX(cp.created_at) as last_payment_date
        FROM customer_accounts ca
        LEFT JOIN customer_payments cp ON ca.id = cp.customer_id
        $where_clause
        GROUP BY ca.id
        ORDER BY ca.created_at DESC
    ";
}
$customers_result = mysqli_query($conn, $customers_query);
$customers = [];
if ($customers_result) {
    $customers = mysqli_fetch_all($customers_result, MYSQLI_ASSOC);
}

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Customer Accounts</h1>
                <p class="page-subtitle">Manage customer accounts and activities</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <form method="GET" class="d-flex gap-2">
                        <input type="text" class="form-control" name="search" placeholder="Search customers..." 
                               value="<?php echo htmlspecialchars($search); ?>">
                        <select class="form-select" name="filter" onchange="this.form.submit()">
                            <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Status</option>
                            <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            <option value="suspended" <?php echo $filter === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                        </select>
                        <button type="submit" class="btn btn-primary">
                            <i class="ri-search-line"></i>
                        </button>
                    </form>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="ri-check-line me-2"></i><?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="ri-error-warning-line me-2"></i><?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Customer Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-user-line text-primary"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo array_sum($stats); ?></h3>
                            <p>Total Customers</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-user-check-line text-success"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['active'] ?? 0; ?></h3>
                            <p>Active Customers</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-user-unfollow-line text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['inactive'] ?? 0; ?></h3>
                            <p>Inactive Customers</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-user-forbid-line text-danger"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $stats['suspended'] ?? 0; ?></h3>
                            <p>Suspended Customers</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customers Table -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Customer Accounts</h3>
                </div>
                <div class="card-body">
                    <?php if (empty($customers)): ?>
                        <div class="text-center py-4">
                            <i class="ri-user-line text-muted" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">No customers found</h5>
                            <p class="text-muted">Customer accounts will appear here when they register.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Contact</th>
                                        <th>Activity</th>
                                        <th>Revenue</th>
                                        <th>Status</th>
                                        <th>Joined</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($customers as $customer): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($customer['customer_name']); ?></strong>
                                                    <br><small class="text-muted">ID: #<?php echo $customer['id']; ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <i class="ri-whatsapp-line text-success me-1"></i>
                                                    <?php echo htmlspecialchars($customer['whatsapp_number']); ?>
                                                    <?php if ($customer['email']): ?>
                                                        <br><small class="text-muted">
                                                            <i class="ri-mail-line me-1"></i><?php echo htmlspecialchars($customer['email']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <small class="badge bg-info"><?php echo $customer['total_payments']; ?> Payments</small>
                                                    <br><small class="badge bg-secondary"><?php echo $customer['total_ads']; ?> Ads</small>
                                                    <?php if ($customer['last_payment_date']): ?>
                                                        <br><small class="text-muted">Last: <?php echo date('M j, Y', strtotime($customer['last_payment_date'])); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <strong>৳<?php echo number_format($customer_revenue[$customer['id']] ?? 0, 2); ?></strong>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = [
                                                    'active' => 'success',
                                                    'inactive' => 'warning',
                                                    'suspended' => 'danger'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_class[$customer['status']] ?? 'secondary'; ?>">
                                                    <?php echo ucfirst($customer['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small><?php echo date('M j, Y', strtotime($customer['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-info" onclick="viewCustomer(<?php echo htmlspecialchars(json_encode($customer)); ?>)">
                                                        <i class="ri-eye-line"></i>
                                                    </button>
                                                    <button class="btn btn-outline-primary" onclick="editCustomer(<?php echo $customer['id']; ?>, '<?php echo $customer['status']; ?>')">
                                                        <i class="ri-edit-line"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary" onclick="addNotes(<?php echo $customer['id']; ?>, '<?php echo htmlspecialchars($customer['admin_notes'] ?? ''); ?>')">
                                                        <i class="ri-sticky-note-line"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Customer View Modal -->
<div class="modal fade" id="viewCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Customer Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="viewCustomerContent">
                <!-- Customer details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Customer Modal -->
<div class="modal fade" id="editCustomerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Customer Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="customer_id" id="edit_customer_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="status" id="edit_customer_status" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="suspended">Suspended</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Customer Notes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="customer_id" id="notes_customer_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Admin Notes</label>
                        <textarea class="form-control" name="notes" id="customer_notes" rows="4"
                                  placeholder="Add notes about this customer..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_notes" class="btn btn-primary">Save Notes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function viewCustomer(customer) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <p><strong>Name:</strong> ${customer.customer_name}</p>
                <p><strong>WhatsApp:</strong> ${customer.whatsapp_number}</p>
                ${customer.email ? `<p><strong>Email:</strong> ${customer.email}</p>` : ''}
                <p><strong>Status:</strong> <span class="badge bg-${customer.status === 'active' ? 'success' : customer.status === 'inactive' ? 'warning' : 'danger'}">${customer.status}</span></p>
                <p><strong>Joined:</strong> ${new Date(customer.created_at).toLocaleDateString()}</p>
            </div>
            <div class="col-md-6">
                <h6>Activity Summary</h6>
                <p><strong>Total Payments:</strong> ${customer.total_payments}</p>
                <p><strong>Total Ads:</strong> ${customer.total_ads}</p>
                ${customer.last_payment_date ? `<p><strong>Last Payment:</strong> ${new Date(customer.last_payment_date).toLocaleDateString()}</p>` : '<p><strong>Last Payment:</strong> Never</p>'}
                <p><strong>Total Spent:</strong> ৳${customer.total_spent || '0.00'}</p>
            </div>
        </div>
        ${customer.admin_notes ? `<hr><div><h6>Admin Notes</h6><p>${customer.admin_notes}</p></div>` : ''}
    `;

    document.getElementById('viewCustomerContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('viewCustomerModal')).show();
}

function editCustomer(customerId, currentStatus) {
    document.getElementById('edit_customer_id').value = customerId;
    document.getElementById('edit_customer_status').value = currentStatus;

    new bootstrap.Modal(document.getElementById('editCustomerModal')).show();
}

function addNotes(customerId, currentNotes) {
    document.getElementById('notes_customer_id').value = customerId;
    document.getElementById('customer_notes').value = currentNotes;

    new bootstrap.Modal(document.getElementById('notesModal')).show();
}
</script>

<style>
/* Stat cards styling */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-icon .text-primary {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.stat-icon .text-success {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.stat-icon .text-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.stat-icon .text-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #111827;
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-weight: 500;
}

/* Dashboard card styling */
.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

/* Button improvements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-group-sm .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    min-width: 32px;
    min-height: 32px;
}

/* Table improvements */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

/* Alert improvements */
.alert {
    border-radius: 8px;
    border: none;
    margin-bottom: 1.5rem;
}

/* Badge improvements */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* Form improvements */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .header-actions {
        width: 100%;
    }

    .header-actions .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}
</style>

<?php include 'includes/footer.php'; ?>
