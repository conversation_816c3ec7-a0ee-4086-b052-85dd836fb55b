<?php
/**
 * Test Script for Custom Ads System Fixes
 * This script tests all the fixes applied to the custom ads system
 */

session_start();
require_once 'includes/config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_username'])) {
        header('Location: login.php');
        exit();
    }
}

$test_results = [];

// Function to test database table existence
function testTableExists($conn, $tableName) {
    $result = mysqli_query($conn, "SHOW TABLES LIKE '$tableName'");
    return mysqli_num_rows($result) > 0;
}

// Function to test column existence
function testColumnExists($conn, $tableName, $columnName) {
    if (!testTableExists($conn, $tableName)) {
        return false;
    }
    $result = mysqli_query($conn, "SHOW COLUMNS FROM `$tableName` LIKE '$columnName'");
    return mysqli_num_rows($result) > 0;
}

// Function to test SQL query
function testQuery($conn, $sql, $description) {
    try {
        $result = mysqli_query($conn, $sql);
        if ($result) {
            return [
                'status' => 'success',
                'description' => $description,
                'message' => 'Query executed successfully'
            ];
        } else {
            return [
                'status' => 'error',
                'description' => $description,
                'message' => 'Query failed: ' . mysqli_error($conn)
            ];
        }
    } catch (Exception $e) {
        return [
            'status' => 'error',
            'description' => $description,
            'message' => 'Exception: ' . $e->getMessage()
        ];
    }
}

// Test 1: Check if all required tables exist
$required_tables = ['customer_accounts', 'customer_payments', 'payment_methods', 'ad_packages', 'custom_ads'];
foreach ($required_tables as $table) {
    $exists = testTableExists($conn, $table);
    $test_results[] = [
        'status' => $exists ? 'success' : 'error',
        'description' => "Table '$table' exists",
        'message' => $exists ? 'Table found' : 'Table missing'
    ];
}

// Test 2: Check if required columns exist in custom_ads table
$required_columns = ['customer_id', 'created_at', 'updated_at', 'is_approved', 'package_id', 'payment_id'];
foreach ($required_columns as $column) {
    $exists = testColumnExists($conn, 'custom_ads', $column);
    $test_results[] = [
        'status' => $exists ? 'success' : 'error',
        'description' => "Column 'custom_ads.$column' exists",
        'message' => $exists ? 'Column found' : 'Column missing'
    ];
}

// Test 3: Test customer-accounts.php SQL query
if (testTableExists($conn, 'customer_accounts') && testTableExists($conn, 'customer_payments')) {
    // Check if created_at column exists in customer_accounts table
    $has_created_at = testColumnExists($conn, 'customer_accounts', 'created_at');
    $order_by = $has_created_at ? "ORDER BY ca.created_at DESC" : "ORDER BY ca.id DESC";

    $sql = "SELECT ca.*, COUNT(DISTINCT cp.id) as total_payments, MAX(cp.created_at) as last_payment_date
            FROM customer_accounts ca
            LEFT JOIN customer_payments cp ON ca.id = cp.customer_id
            GROUP BY ca.id
            $order_by
            LIMIT 1";
    $test_results[] = testQuery($conn, $sql, 'customer-accounts.php SQL query');
}

// Test 4: Test custom-ads-analytics.php SQL query
if (testTableExists($conn, 'custom_ads')) {
    $has_created_at = testColumnExists($conn, 'custom_ads', 'created_at');
    $has_is_approved = testColumnExists($conn, 'custom_ads', 'is_approved');

    if ($has_created_at && $has_is_approved) {
        $sql = "SELECT COUNT(*) as total_ads,
                COUNT(CASE WHEN is_approved = 1 THEN 1 END) as approved_ads
                FROM custom_ads
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
    } else {
        $sql = "SELECT COUNT(*) as total_ads, 0 as approved_ads FROM custom_ads";
    }
    $test_results[] = testQuery($conn, $sql, 'custom-ads-analytics.php SQL query');
}

// Test 5: Test customads-provider/payments.php SQL query
if (testTableExists($conn, 'customer_payments') && testTableExists($conn, 'ad_packages') && testTableExists($conn, 'customer_accounts')) {
    $sql = "SELECT cp.*, ap.package_name 
            FROM customer_payments cp 
            LEFT JOIN ad_packages ap ON cp.package_id = ap.id 
            WHERE cp.customer_id = (SELECT id FROM customer_accounts WHERE whatsapp_number = '***********') 
            ORDER BY cp.created_at DESC 
            LIMIT 1";
    $test_results[] = testQuery($conn, $sql, 'customads-provider/payments.php SQL query');
}

// Test 6: Test payment-methods.php functionality
if (testTableExists($conn, 'payment_methods')) {
    $sql = "SELECT * FROM payment_methods ORDER BY display_order ASC, display_name ASC LIMIT 1";
    $test_results[] = testQuery($conn, $sql, 'payment-methods.php SQL query');
}

// Test 7: Check if default data exists
if (testTableExists($conn, 'payment_methods')) {
    $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM payment_methods");
    $count = mysqli_fetch_assoc($count_result)['count'];
    $test_results[] = [
        'status' => $count > 0 ? 'success' : 'warning',
        'description' => 'Default payment methods exist',
        'message' => $count > 0 ? "$count payment methods found" : 'No payment methods found'
    ];
}

if (testTableExists($conn, 'ad_packages')) {
    $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM ad_packages");
    $count = mysqli_fetch_assoc($count_result)['count'];
    $test_results[] = [
        'status' => $count > 0 ? 'success' : 'warning',
        'description' => 'Default ad packages exist',
        'message' => $count > 0 ? "$count ad packages found" : 'No ad packages found'
    ];
}

// Test 8: Test page accessibility
$pages_to_test = [
    'payment-verification.php' => 'Payment Verification Page',
    'customer-accounts.php' => 'Customer Accounts Page',
    'custom-ads-analytics.php' => 'Custom Ads Analytics Page',
    'payment-methods.php' => 'Payment Methods Page'
];

include 'includes/header.php';
?>

<div class="admin-container">
    <!-- Sidebar -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="content-header">
            <div class="header-left">
                <h1 class="page-title">Custom Ads System Test Results</h1>
                <p class="page-subtitle">Verification of all fixes applied to the custom ads system</p>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="ri-refresh-line me-1"></i>Refresh Tests
                    </button>
                </div>
            </div>
        </header>

        <!-- Content Body -->
        <div class="content-body">
            <!-- Test Results Summary -->
            <div class="row mb-4">
                <?php
                $success_count = count(array_filter($test_results, function($test) { return $test['status'] === 'success'; }));
                $error_count = count(array_filter($test_results, function($test) { return $test['status'] === 'error'; }));
                $warning_count = count(array_filter($test_results, function($test) { return $test['status'] === 'warning'; }));
                $total_tests = count($test_results);
                ?>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-check-line text-success"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $success_count; ?></h3>
                            <p>Tests Passed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-close-line text-danger"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $error_count; ?></h3>
                            <p>Tests Failed</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-alert-line text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $warning_count; ?></h3>
                            <p>Warnings</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="ri-list-check-line text-info"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $total_tests; ?></h3>
                            <p>Total Tests</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Test Results -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Detailed Test Results</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Status</th>
                                    <th>Test Description</th>
                                    <th>Result Message</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($test_results as $test): ?>
                                    <tr>
                                        <td>
                                            <?php if ($test['status'] === 'success'): ?>
                                                <span class="badge bg-success"><i class="ri-check-line"></i> Pass</span>
                                            <?php elseif ($test['status'] === 'error'): ?>
                                                <span class="badge bg-danger"><i class="ri-close-line"></i> Fail</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning"><i class="ri-alert-line"></i> Warning</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($test['description']); ?></td>
                                        <td><?php echo htmlspecialchars($test['message']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Page Links for Testing -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Test Fixed Pages</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($pages_to_test as $page => $title): ?>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                                    <div>
                                        <h6 class="mb-1"><?php echo $title; ?></h6>
                                        <small class="text-muted"><?php echo $page; ?></small>
                                    </div>
                                    <a href="<?php echo $page; ?>" class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="ri-external-link-line me-1"></i>Test Page
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Recommendations</h3>
                </div>
                <div class="card-body">
                    <?php if ($error_count > 0): ?>
                        <div class="alert alert-danger">
                            <h6><i class="ri-error-warning-line me-2"></i>Action Required</h6>
                            <p>Some tests failed. Please run the database fix script to resolve missing tables or columns:</p>
                            <a href="fix_custom_ads_database.php" class="btn btn-danger btn-sm">
                                <i class="ri-tools-line me-1"></i>Run Database Fix
                            </a>
                        </div>
                    <?php elseif ($warning_count > 0): ?>
                        <div class="alert alert-warning">
                            <h6><i class="ri-alert-line me-2"></i>Recommendations</h6>
                            <p>Some warnings were found. Consider adding default data if missing.</p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <h6><i class="ri-check-line me-2"></i>All Tests Passed!</h6>
                            <p>The custom ads system is working correctly. All database issues have been resolved.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
</div>

<style>
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-icon .text-success { background: rgba(34, 197, 94, 0.1); color: #22c55e; }
.stat-icon .text-danger { background: rgba(239, 68, 68, 0.1); color: #ef4444; }
.stat-icon .text-warning { background: rgba(245, 158, 11, 0.1); color: #f59e0b; }
.stat-icon .text-info { background: rgba(59, 130, 246, 0.1); color: #3b82f6; }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #111827;
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-weight: 500;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}
</style>

<?php include 'includes/footer.php'; ?>
