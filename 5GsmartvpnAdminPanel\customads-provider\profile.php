<?php
/**
 * Custom Ads Provider - Profile Page
 */

session_start();
require_once '../admin_new/includes/config.php';

if (!isset($_SESSION['provider_id'])) {
    header("Location: login.php");
    exit;
}

// Get provider information
$stmt = $conn->prepare("SELECT * FROM custom_ads_providers WHERE provider_id = ?");
$stmt->bind_param("s", $_SESSION['provider_id']);
$stmt->execute();
$provider = $stmt->get_result()->fetch_assoc();

$success = '';
$error = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $business_name = trim($_POST['business_name']);
    $contact_person = trim($_POST['contact_person']);
    $email = trim($_POST['email']);
    $whatsapp_number = trim($_POST['whatsapp_number']);
    $business_address = trim($_POST['business_address']);
    
    if (empty($business_name) || empty($contact_person) || empty($whatsapp_number)) {
        $error = 'Please fill in all required fields.';
    } else {
        $stmt = $conn->prepare("UPDATE custom_ads_providers SET business_name = ?, contact_person = ?, email = ?, whatsapp_number = ?, business_address = ? WHERE provider_id = ?");
        $stmt->bind_param("ssssss", $business_name, $contact_person, $email, $whatsapp_number, $business_address, $_SESSION['provider_id']);
        
        if ($stmt->execute()) {
            $success = 'Profile updated successfully!';
            // Refresh provider data
            $stmt = $conn->prepare("SELECT * FROM custom_ads_providers WHERE provider_id = ?");
            $stmt->bind_param("s", $_SESSION['provider_id']);
            $stmt->execute();
            $provider = $stmt->get_result()->fetch_assoc();
        } else {
            $error = 'Failed to update profile.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - 5G Smart VPN Custom Ads</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            margin: 5px 0;
        }
        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .profile-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <div class="text-center mb-4">
                    <h5 class="text-white">5G Smart VPN</h5>
                    <small class="text-white-50">Custom Ads Provider</small>
                </div>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a class="nav-link" href="ads.php">
                        <i class="fas fa-ad me-2"></i> My Ads
                    </a>
                    <a class="nav-link" href="create-ad.php">
                        <i class="fas fa-plus-circle me-2"></i> Create Ad
                    </a>
                    <a class="nav-link" href="analytics.php">
                        <i class="fas fa-chart-bar me-2"></i> Analytics
                    </a>
                    <a class="nav-link" href="packages.php">
                        <i class="fas fa-box me-2"></i> Packages
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i> Payments
                    </a>
                    <a class="nav-link active" href="profile.php">
                        <i class="fas fa-user me-2"></i> Profile
                    </a>
                    <hr class="text-white-50">
                    <a class="nav-link" href="?logout=1">
                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                    </a>
                </nav>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-4">
                <h2 class="mb-4">Profile Settings</h2>
                
                <?php if ($success): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Profile Form -->
                    <div class="col-lg-8">
                        <div class="card profile-card">
                            <div class="card-header">
                                <h5 class="mb-0">Business Information</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="business_name" class="form-label">Business Name *</label>
                                        <input type="text" class="form-control" id="business_name" name="business_name" 
                                               value="<?php echo htmlspecialchars($provider['business_name']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="contact_person" class="form-label">Contact Person *</label>
                                        <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                               value="<?php echo htmlspecialchars($provider['contact_person']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($provider['email'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="whatsapp_number" class="form-label">WhatsApp Number *</label>
                                        <input type="text" class="form-control" id="whatsapp_number" name="whatsapp_number" 
                                               value="<?php echo htmlspecialchars($provider['whatsapp_number']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="business_address" class="form-label">Business Address</label>
                                        <textarea class="form-control" id="business_address" name="business_address" rows="3"><?php echo htmlspecialchars($provider['business_address'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <button type="submit" name="update_profile" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Profile
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Info -->
                    <div class="col-lg-4">
                        <div class="card profile-card">
                            <div class="card-header">
                                <h5 class="mb-0">Account Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Provider ID</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($provider['provider_id']); ?>" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Account Status</label>
                                    <div>
                                        <span class="badge bg-<?php echo $provider['status'] === 'active' ? 'success' : 'warning'; ?> fs-6">
                                            <?php echo ucfirst($provider['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Member Since</label>
                                    <input type="text" class="form-control" value="<?php echo $provider['created_at'] ? date('M j, Y', strtotime($provider['created_at'])) : 'Not Available'; ?>" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Verification Status</label>
                                    <div>
                                        <span class="badge bg-<?php echo $provider['is_verified'] ? 'success' : 'warning'; ?> fs-6">
                                            <?php echo $provider['is_verified'] ? 'Verified' : 'Pending'; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card profile-card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">Support</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Need help with your account?</p>
                                <a href="https://wa.me/**********" target="_blank" class="btn btn-success w-100">
                                    <i class="fab fa-whatsapp me-2"></i>Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
